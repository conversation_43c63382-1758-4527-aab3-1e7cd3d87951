const axios = require("axios");
const { QuickDB } = require("quick.db");
const db = new QuickDB();

module.exports = {
  command: "chat",
  aliases: ["gpt", "wahai"],
  category: "utility",
  description: "Menggunakan G4F API untuk mendapatkan balasan",
  requiresPrefix: true,
  includes: false,
  handler: async (client, blobClient, event, args) => {
    if (!args.length) {
      await client.replyMessage({
        replyToken: event.replyToken,
        messages: [
          { 
            type: "text", 
            text: "Kirimkan pesan untuk diproses: !chat <pesan>",
            quoteToken: event.message.quoteToken,
          }
        ]
      });
      return;
    }

    let userId = event.source.userId;
    if (event.source.type === "group") {
      userId = `${event.source.groupId}:${event.source.userId}`;
    }

    let user;
    let group;

    if (event.source.type === "group") {
      user = await client.getGroupMemberProfile(event.source.groupId, event.source.userId);
      group = await client.getGroupSummary(event.source.groupId);
    } else {
      user = await client.getProfile(event.source.userId);
      group = "You're in one-on-one chat with user"
    }

    const userMessage = args.join(" ");
    const userName = user.displayName;
    const groupName = group.groupName;

    // Ambil riwayat percakapan sebelumnya dari database
    let previousMessages = (await db.get(`chatHistory_${userId}`)) || [];

    // Hapus riwayat paling lama jika sudah mencapai 40
    if (previousMessages.length >= 40) {
      previousMessages.shift();
    }

    // Tambahkan pesan pengguna ke riwayat
    previousMessages.push({
      role: "user",
      content: userMessage,
    });

    try {
      // Deteksi apakah alias yang dipakai adalah "wahai"
      const isKerangAjaibMode = event.message.text.startsWith("!wahai");

      // Buat system prompt sesuai mode
      const systemPrompt = isKerangAjaibMode
        ? `
          Kamu adalah Kerang Ajaib dari Spongebob.
          Tugasmu:
          - Hanya menjawab dengan 1 hingga 3 kata saja.
          - Gunakan bahasa Indonesia.
          - Jawaban harus terdengar misterius, ambigu, atau tidak jelas.
          - Contoh: "Tidak bisa", "Tentu saja", "Mungkin nanti"
        `
        : `
          Introduction:
          You're a bot named BaquaBot created by Tama-chan for LINE Messanger Group chat. and you can speak indonesian, english and japanese.
          Respond in plain text only. Do not use markdown, code blocks, bullet points, or any special formatting. All responses should be written as continuous text.

          You have sister named Rikai 理解 also from the same creator, she is a Discord bot.
          
          Task:
          - Chat as naturally as possible with the user.
          - Simulate human conversation with the user.
          - Provide reliable sources of information such as Wikipedia.
          - When users ask for your command list, tell them to type "!help".
          - Occasionally roast users humorously.
          - Avoid replying in Spanish unless initiated by the user.
          - Be fun

          Current User:
          - Name: ${userName}
          - Group Name: ${groupName}
        `;

      // Kirim pesan ke API Python
      const response = await axios.post("http://localhost:5000/chat", {
        messages: [
          { 
            role: "system", 
            content: systemPrompt
          },
          ...previousMessages
        ]
      });

      const reply = response.data;

      // Simpan balasan ke history
      previousMessages.push({
        role: "assistant",
        content: reply,
      });

      // Simpan kembali ke database
      await db.set(`chatHistory_${userId}`, previousMessages);

      // Balas ke user
      await client.replyMessage({
        replyToken: event.replyToken,
        messages: [
          { 
            type: "text", 
            text: reply,
            quoteToken: event.message.quoteToken,
          }
        ]
      });
    } catch (error) {
      console.error("Error contacting Python API:", error);
      await client.replyMessage({
        replyToken: event.replyToken,
        messages: [
          { 
            type: "text", 
            text: "Terjadi kesalahan saat memproses pesan Anda.",
            quoteToken: event.message.quoteToken,
          }
        ]
      });
    }
  }
};
