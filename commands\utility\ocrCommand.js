const { QuickDB } = require("quick.db");
const db = new QuickDB();
const { setSessionTimeout } = require("../../utils/sessionTimeouts");
const axios = require("axios");
const fs = require("fs");
const path = require("path");
const { generateApiUrl } = require("../../utils/urlSigner");

module.exports = {
  command: "ocr",
  aliases: [],
  category: "utility",
  description: "Melakukan OCR pada gambar yang diunggah.",
  requiresPrefix: true,
  includes: false,
  handler: async (client, blobClient, event, args) => {
    let userId = event.source.userId;
    if (event.source.type === "group") {
      userId = `${event.source.groupId}:${event.source.userId}`;
    }
    const question =
      args.join(" ") || "Describe this image, and reply in unformatted plain text ";

    if (event.message.quotedMessageId) {
      const quotedMessageId = event.message.quotedMessageId;
      let stream = await blobClient.getMessageContent(quotedMessageId);
      let chunks = [];

      stream.on("data", (chunk) => {
        chunks.push(chunk);
      });

      stream.on("end", async () => {
        const buffer = Buffer.concat(chunks);
        const timestamp = Date.now();
        const dirPath = path.join(__dirname, "../../static/downloads");
        const filePath = path.join(dirPath, `${timestamp}.jpg`);

        if (!fs.existsSync(dirPath)) {
          fs.mkdirSync(dirPath, { recursive: true });
        }

        fs.writeFileSync(filePath, buffer);
        console.log(`Gambar berhasil disimpan di ${filePath}`);

        const imageUrl = generateApiUrl(`/downloads/${timestamp}.jpg`);

        try {
          const chatHistory = (await db.get(`chatHistory_${userId}`)) || [];
          const response = await axios.post(
            "http://127.0.0.1:5000/analyze_image",
            {
              image_url: imageUrl,
              question: question,
            }
          );

          const answer = response.data.answer;

          chatHistory.push({ role: "user", content: question });
          chatHistory.push({ role: "assistant", content: answer });
          await db.set(`chatHistory_${userId}`, chatHistory);

          await client.replyMessage({
            replyToken: event.replyToken,
            messages: [
              {
                type: "text",
                text: `${answer}`,
                quoteToken: event.message.quoteToken,
              },
            ],
          });
        } catch (error) {
          console.error("Error analyzing image:", error);
          await client.replyMessage({
            replyToken: event.replyToken,
            messages: [
              {
                type: "text",
                text: "Terjadi kesalahan saat menganalisis gambar.",
                quoteToken: event.message.quoteToken,
              },
            ],
          });
        } finally {
          fs.unlink(filePath, (err) => {
            if (err)
              console.error(`Failed to delete image file: ${filePath}`, err);
            else console.log(`Successfully deleted image file: ${filePath}`);
          });
        }
      });

      stream.on("error", async (err) => {
        console.error("Error downloading content:", err);
        return client.replyMessage({
          replyToken: event.replyToken,
          messages: [
            {
              type: "text",
              text: "An error occurred while downloading the image.",
              quoteToken: event.message.quoteToken,
            },
          ],
        });
      });
    } else {
      await db.set(`ocr_${userId}_active`, true);
      if (question) {
        await db.set(`ocr_question_${userId}`, question);
      }

      await client.replyMessage({
        replyToken: event.replyToken,
        messages: [
          {
            type: "text",
            text: "Please Upload an Image to Perform OCR.",
            quoteToken: event.message.quoteToken,
          },
        ],
      });

      setSessionTimeout(userId, "ocr", async () => {
        console.log(`OCR session for user ${userId} timed out.`);
        await db.delete(`ocr_${userId}_active`);
        await db.delete(`ocr_question_${userId}`);
      });
    }
  },
};